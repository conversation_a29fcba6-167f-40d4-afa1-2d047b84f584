"""
SM2加密工具类
使用gmssl库实现SM2加密功能
"""
import base64
from typing import Optional

try:
    from gmssl import sm2
    GMSSL_AVAILABLE = True
except ImportError:
    GMSSL_AVAILABLE = False
    print("警告: gmssl库未正确安装，将使用模拟加密")

class SM2Utils:
    """SM2加密工具类"""

    @staticmethod
    def encrypt_by_public_key(data: str, public_key: str) -> Optional[str]:
        """
        使用公钥加密数据

        Args:
            data: 要加密的数据
            public_key: 公钥字符串

        Returns:
            加密后的base64编码字符串，失败返回None
        """
        if not GMSSL_AVAILABLE:
            # 模拟加密 - 仅用于演示
            print("使用模拟加密（仅用于演示）")
            mock_encrypted = base64.b64encode(f"MOCK_ENCRYPTED_{data}".encode('utf-8')).decode('utf-8')
            return mock_encrypted

        try:
            
            # 注意：因为没有私钥，所以传入的私钥参数可以为空或 None
            sm2_crypt = sm2.Crypt(public_key, None)
            # 加密（使用公钥）
            encrypted_data = sm2_crypt.encrypt(data.encode('utf-8'))

            # 返回base64编码的结果
            return  encrypted_data.hex()

        except Exception as e:
            print(f"SM2加密失败: {str(e)}")
            return None

    @staticmethod
    def decrypt_by_private_key(encrypted_data: str, private_key: str) -> Optional[str]:
        """
        使用私钥解密数据

        Args:
            encrypted_data: base64编码的加密数据
            private_key: 私钥字符串

        Returns:
            解密后的字符串，失败返回None
        """
        try:
            # 解码加密数据
            encrypted_bytes = base64.b64decode(encrypted_data)

            # 创建SM2解密对象
            sm2_crypt = sm2.CryptSM2(public_key="", private_key=private_key)

            # 解密数据
            decrypted_data = sm2_crypt.decrypt(encrypted_bytes)

            return decrypted_data.decode('utf-8')

        except Exception as e:
            print(f"SM2解密失败: {str(e)}")
            return None
